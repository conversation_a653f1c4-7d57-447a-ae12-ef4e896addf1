import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsUrl, <PERSON><PERSON>ength, <PERSON><PERSON><PERSON>th, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ToolRequestPriority, ToolRequestStatus } from '../../../generated/prisma';

export class CreateToolRequestDto {
  @ApiProperty({ 
    description: 'Name of the requested tool',
    example: 'ChatGPT Alternative',
    maxLength: 255
  })
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  toolName: string;

  @ApiProperty({ 
    description: 'Detailed description of the tool',
    example: 'An AI-powered chatbot that can help with coding tasks and general questions',
    maxLength: 2000
  })
  @IsString()
  @MinLength(10)
  @MaxLength(2000)
  description: string;

  @ApiProperty({ 
    description: 'Reason why this tool should be added',
    example: 'This tool would be valuable for developers who need AI assistance with coding',
    maxLength: 1000
  })
  @IsString()
  @MinLength(10)
  @MaxLength(1000)
  reason: string;

  @ApiPropertyOptional({ 
    description: 'Suggested category for the tool',
    example: 'AI Tools',
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  categorySuggestion?: string;

  @ApiPropertyOptional({ 
    description: 'Website URL of the tool',
    example: 'https://example.com'
  })
  @IsOptional()
  @IsUrl()
  websiteUrl?: string;

  @ApiPropertyOptional({ 
    enum: ToolRequestPriority,
    description: 'Priority level of the request',
    default: ToolRequestPriority.MEDIUM
  })
  @IsOptional()
  @IsEnum(ToolRequestPriority)
  priority?: ToolRequestPriority;
}

export class ToolRequestResponseDto {
  @ApiProperty({ description: 'Unique identifier for the tool request' })
  id: string;

  @ApiProperty({ description: 'User ID who made the request' })
  userId: string;

  @ApiProperty({ description: 'Name of the requested tool' })
  toolName: string;

  @ApiProperty({ description: 'Detailed description of the tool' })
  description: string;

  @ApiProperty({ description: 'Reason why this tool should be added' })
  reason: string;

  @ApiPropertyOptional({ description: 'Suggested category for the tool' })
  categorySuggestion?: string;

  @ApiPropertyOptional({ description: 'Website URL of the tool' })
  websiteUrl?: string;

  @ApiProperty({ enum: ToolRequestPriority, description: 'Priority level of the request' })
  priority: ToolRequestPriority;

  @ApiProperty({ enum: ToolRequestStatus, description: 'Current status of the request' })
  status: ToolRequestStatus;

  @ApiPropertyOptional({ description: 'Admin notes on the request' })
  adminNotes?: string;

  @ApiProperty({ description: 'Number of votes for this request' })
  votes: number;

  @ApiProperty({ description: 'Timestamp when the request was created' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp when the request was last updated' })
  updatedAt: Date;
}

export class ListToolRequestsDto {
  @ApiPropertyOptional({ description: 'Page number', default: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'page must be an integer' })
  @Min(1, { message: 'page must be at least 1' })
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Number of items per page', default: 20, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'limit must be an integer' })
  @Min(1, { message: 'limit must be at least 1' })
  @Max(100, { message: 'limit cannot exceed 100' })
  limit?: number = 20;

  @ApiPropertyOptional({ enum: ToolRequestStatus, description: 'Filter by status' })
  @IsOptional()
  @IsEnum(ToolRequestStatus, { message: 'Invalid tool request status provided' })
  status?: ToolRequestStatus;

  @ApiPropertyOptional({ enum: ToolRequestPriority, description: 'Filter by priority' })
  @IsOptional()
  @IsEnum(ToolRequestPriority, { message: 'Invalid tool request priority provided' })
  priority?: ToolRequestPriority;
}

export class PaginatedToolRequestsResponseDto {
  @ApiProperty({ type: [ToolRequestResponseDto], description: 'List of tool requests' })
  data: ToolRequestResponseDto[];

  @ApiProperty({ description: 'Pagination metadata' })
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
