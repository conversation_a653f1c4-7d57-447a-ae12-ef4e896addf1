import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, IsOptional, Max, Min, IsEnum } from 'class-validator';
import { SubmissionStatus } from '../../../generated/prisma';

export class UserSubmittedToolResponseDto {
  @ApiProperty({ description: 'Unique identifier for the submission record' })
  id: string;

  @ApiProperty({ description: 'User ID who submitted the tool' })
  userId: string;

  @ApiProperty({ description: 'Entity ID of the submitted tool' })
  entityId: string;

  @ApiProperty({ enum: SubmissionStatus, description: 'Current status of the submission' })
  submissionStatus: SubmissionStatus;

  @ApiProperty({ description: 'Timestamp when the tool was submitted' })
  submittedAt: Date;

  @ApiPropertyOptional({ description: 'Timestamp when the submission was reviewed' })
  reviewedAt?: Date;

  @ApiPropertyOptional({ description: 'ID of the reviewer' })
  reviewerId?: string;

  @ApiPropertyOptional({ description: 'Notes from the reviewer' })
  reviewerNotes?: string;

  @ApiPropertyOptional({ description: 'Changes requested by the reviewer' })
  changesRequested?: string;

  @ApiProperty({ description: 'Full entity details' })
  entity: {
    id: string;
    name: string;
    slug: string;
    websiteUrl?: string;
    shortDescription?: string;
    description?: string;
    logoUrl?: string;
    status: string;
    createdAt: Date;
    updatedAt: Date;
    entityType: {
      id: string;
      name: string;
      slug: string;
    };
    entityCategories: Array<{
      category: {
        id: string;
        name: string;
        slug: string;
      };
    }>;
    entityTags: Array<{
      tag: {
        id: string;
        name: string;
        slug: string;
      };
    }>;
  };
}

export class ListUserSubmittedToolsDto {
  @ApiPropertyOptional({ description: 'Page number', default: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'page must be an integer' })
  @Min(1, { message: 'page must be at least 1' })
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Number of items per page', default: 20, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'limit must be an integer' })
  @Min(1, { message: 'limit must be at least 1' })
  @Max(100, { message: 'limit cannot exceed 100' })
  limit?: number = 20;

  @ApiPropertyOptional({ enum: SubmissionStatus, description: 'Filter by submission status' })
  @IsOptional()
  @IsEnum(SubmissionStatus, { message: 'Invalid submission status provided' })
  status?: SubmissionStatus;
}

export class PaginatedUserSubmittedToolsResponseDto {
  @ApiProperty({ type: [UserSubmittedToolResponseDto], description: 'List of submitted tools' })
  data: UserSubmittedToolResponseDto[];

  @ApiProperty({ description: 'Pagination metadata' })
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
