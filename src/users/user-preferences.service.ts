import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ValidationService } from '../common/validation.service';
import { UpdateUserPreferencesDto, UserPreferencesResponseDto } from './dto/user-preferences.dto';
import { UserPreferences } from '../../generated/prisma';

@Injectable()
export class UserPreferencesService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly validationService: ValidationService,
  ) {}

  async getUserPreferences(userId: string): Promise<UserPreferencesResponseDto> {
    // Use upsert to atomically find or create preferences with defaults
    const preferences = await this.prismaService.userPreferences.upsert({
      where: { userId },
      update: {}, // Don't update anything if found
      create: {
        userId,
        emailNotifications: true,
        marketingEmails: false,
        weeklyDigest: true,
        newToolAlerts: true,
        profileVisibility: 'PUBLIC',
        showBookmarks: true,
        showReviews: true,
        showActivity: true,
        theme: 'LIGHT',
        itemsPerPage: 20,
        defaultView: 'GRID',
        preferredCategories: [],
        blockedCategories: [],
        contentLanguage: 'en',
      },
    });

    return this.mapToResponseDto(preferences);
  }

  async updateUserPreferences(
    userId: string,
    updateDto: UpdateUserPreferencesDto,
  ): Promise<UserPreferencesResponseDto> {
    // Validate the preferences update
    this.validationService.validateUserPreferences(updateDto);

    const preferences = await this.prismaService.userPreferences.upsert({
      where: { userId },
      update: {
        ...updateDto,
        updatedAt: new Date(),
      },
      create: {
        userId,
        ...updateDto,
      },
    });

    return this.mapToResponseDto(preferences);
  }



  private mapToResponseDto(preferences: UserPreferences): UserPreferencesResponseDto {
    return {
      id: preferences.id,
      userId: preferences.userId,
      emailNotifications: preferences.emailNotifications,
      marketingEmails: preferences.marketingEmails,
      weeklyDigest: preferences.weeklyDigest,
      newToolAlerts: preferences.newToolAlerts,
      profileVisibility: preferences.profileVisibility,
      showBookmarks: preferences.showBookmarks,
      showReviews: preferences.showReviews,
      showActivity: preferences.showActivity,
      theme: preferences.theme,
      itemsPerPage: preferences.itemsPerPage,
      defaultView: preferences.defaultView,
      preferredCategories: preferences.preferredCategories,
      blockedCategories: preferences.blockedCategories,
      contentLanguage: preferences.contentLanguage,
      createdAt: preferences.createdAt,
      updatedAt: preferences.updatedAt,
    };
  }
}
