import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Entity, Prisma, UserSavedEntity } from 'generated/prisma'; // Assuming User is Prisma.User
import { ListBookmarksDto } from './dto/list-bookmarks.dto';
import { CreateBookmarkDto } from './dto/create-bookmark.dto';
import { AppLoggerService } from '../common/logger/logger.service';
import { ActivityLoggerService } from '../common/activity-logger.service';
import { ValidationService } from '../common/validation.service';

@Injectable()
export class BookmarksService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logger: AppLoggerService,
    private readonly activityLogger: ActivityLoggerService,
    private readonly validationService: ValidationService,
  ) {}

  async saveBookmark(
    userId: string,
    createBookmarkDto: CreateBookmarkDto,
  ): Promise<UserSavedEntity> {
    const { entity_id } = createBookmarkDto;

    // Validate the bookmark action
    await this.validationService.validateBookmarkAction(userId, entity_id);

    // 1. Verify the entity exists and get its details for activity logging
    const entity = await this.prisma.entity.findUnique({
      where: { id: entity_id },
      select: { id: true, name: true, slug: true },
    });

    if (!entity) {
      throw new NotFoundException(`Entity with ID "${entity_id}" not found.`);
    }

    // 2. Use upsert to create the link or do nothing if it already exists
    // This handles the "already bookmarked" case gracefully due to the unique constraint
    // on (userId, entityId) in the UserSavedEntity table.
    try {
      const savedBookmark = await this.prisma.userSavedEntity.upsert({
        where: {
          userId_entityId: { // This assumes a compound unique key named userId_entityId
            userId: userId,
            entityId: entity_id,
          },
        },
        create: {
          userId: userId,
          entityId: entity_id,
        },
        update: {},
        // Omitting update means if it exists, it's found and returned, no changes made.
        // If it doesn't exist, it's created.
      });

      // Log the bookmark activity
      await this.activityLogger.logBookmarkActivity(
        userId,
        entity_id,
        entity.name,
        entity.slug || entity.id,
        'added'
      );

      return savedBookmark;
    } catch (error) {
      // Log the error with structured logging
      this.logger.logError(error as Error, {
        operation: 'saveBookmark',
        userId,
        entityId: entity_id,
        type: 'bookmark_save_error',
      });

      // Check for specific Prisma errors and provide better error messages
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            // Unique constraint violation - this shouldn't happen with upsert, but just in case
            this.logger.warn('Unique constraint violation during bookmark save', {
              operation: 'saveBookmark',
              userId,
              entityId: entity_id,
              errorCode: error.code,
            });
            // Return the existing bookmark instead of throwing an error
            const existingBookmark = await this.prisma.userSavedEntity.findUnique({
              where: {
                userId_entityId: {
                  userId: userId,
                  entityId: entity_id,
                },
              },
            });
            if (existingBookmark) {
              return existingBookmark;
            }
            break;
          case 'P2003':
            // Foreign key constraint failed - likely invalid userId or entityId
            throw new NotFoundException(`Invalid user ID or entity ID provided.`);
          default:
            this.logger.logError(error, {
              operation: 'saveBookmark',
              userId,
              entityId: entity_id,
              errorCode: error.code,
              type: 'prisma_unknown_error',
            });
            break;
        }
      }

      throw new InternalServerErrorException('Could not save the bookmark. Please try again later.');
    }
  }

  async unsaveBookmark(userId: string, entityId: string): Promise<void> {
    // Get entity details for activity logging before deletion
    const entity = await this.prisma.entity.findUnique({
      where: { id: entityId },
      select: { id: true, name: true, slug: true },
    });

    try {
      await this.prisma.userSavedEntity.delete({
        where: {
          userId_entityId: { // Assumes compound unique key name
            userId: userId,
            entityId: entityId,
          },
        },
      });

      this.logger.log('Bookmark successfully removed', {
        operation: 'unsaveBookmark',
        userId,
        entityId,
      });

      // Log the bookmark removal activity
      if (entity) {
        await this.activityLogger.logBookmarkActivity(
          userId,
          entityId,
          entity.name,
          entity.slug || entity.id,
          'removed'
        );
      }

      // If delete is successful, no return value is needed for a 204 No Content response.
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // P2025: "An operation failed because it depends on one or more records that were required but not found."
        // This means the bookmark to delete didn't exist.
        // For a DELETE operation, this is often treated as idempotent success (already deleted).
        if (error.code === 'P2025') {
          // Log that the bookmark was not found, but don't throw an error.
          this.logger.log('Bookmark not found during delete - treating as success', {
            operation: 'unsaveBookmark',
            userId,
            entityId,
            reason: 'bookmark_not_found',
          });
          return; // Still a success from the client's perspective (it's not bookmarked)
        }
      }

      this.logger.logError(error as Error, {
        operation: 'unsaveBookmark',
        userId,
        entityId,
        type: 'bookmark_delete_error',
      });

      throw new InternalServerErrorException('Could not unsave the bookmark. Please try again later.');
    }
  }

  async listSavedBookmarks(
    userId: string,
    listBookmarksDto: ListBookmarksDto,
  ): Promise<{ data: Entity[]; meta: { total: number; page: number; limit: number; totalPages: number } }> {
    const { page = 1, limit = 10 } = listBookmarksDto;
    const skip = (page - 1) * limit;

    try {
      const [savedJoins, total] = await this.prisma.$transaction([
        this.prisma.userSavedEntity.findMany({
          where: { userId },
          skip,
          take: limit,
          orderBy: {
            // Order by when the bookmark was created, newest first
            createdAt: Prisma.SortOrder.desc,
          },
          include: {
            entity: { // Include the actual entity details
              include: {
                entityType: { select: { id: true, name: true, slug: true } },
                submitter: { select: { id: true, username: true, profilePictureUrl: true } },
                // You might want to include a subset of categories/tags or count them
                // For simplicity, not including all categories/tags here to avoid overly large responses
                // _count: { select: { entityCategories: true, entityTags: true, reviews: true } } // Example for counts
              },
            },
          },
        }),
        this.prisma.userSavedEntity.count({ where: { userId } }),
      ]);

      // Extract the entities from the join records
      const entities = savedJoins.map(joinRecord => joinRecord.entity);

      return {
        data: entities,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.logError(error as Error, {
        operation: 'listSavedBookmarks',
        userId,
        page,
        limit,
        type: 'bookmark_list_error',
      });

      throw new InternalServerErrorException(
        'Could not retrieve saved bookmarks. Please try again later.',
      );
    }
  }
} 